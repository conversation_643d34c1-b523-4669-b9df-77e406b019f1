package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request

import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.doanload.onUs
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import java.math.BigDecimal
import java.math.RoundingMode

data class WaybillRequest(
    var orderNo: String,
    var spu: String,
    var size: String,
    var color: String,
    var qty: Int = 1,
    var weight: BigDecimal = BigDecimal.ZERO,
    var price: BigDecimal = BigDecimal.ZERO,
    var currency: String = "",
    var name: String = "",
    var cnName: String = "",
    var title: String = "",
    var supplierName: String,
    var detail: String,
    var country: String = "",
    var material: String? = null,
    var hsCode: String? = null,
    var totalPrice: BigDecimal? = null,
) {
    fun skuCode(): String = "$spu-$size-$color"

    private fun price(): BigDecimal = 5.toBigDecimal()

    fun price(
        multiSize: Int,
        country: String,
    ): BigDecimal {
        if (totalPrice != null) {
            return totalPrice!!.divide(multiSize.toBigDecimal(), 2, RoundingMode.HALF_UP)
        }

        if ("NL".equals(country, true)) {
            return BigDecimal.valueOf(3)
        }
        if (country.onUs()) {
            val price =
                if (multiSize <= 10) {
                    BigDecimal.valueOf(1).divide(multiSize.toBigDecimal(), 2, RoundingMode.HALF_UP)
                } else if (multiSize <= 20) {
                    BigDecimal.valueOf(5).divide(multiSize.toBigDecimal(), 2, RoundingMode.HALF_UP)
                } else if (multiSize <= 40) {
                    BigDecimal.valueOf(10).divide(multiSize.toBigDecimal(), 2, RoundingMode.HALF_UP)
                } else {
                    throw IllegalArgumentException("美国总件数不能超过40")
                }
            if (price < BigDecimal.valueOf(1)) {
                return BigDecimal.valueOf(1)
            }
        }
        if (multiSize > 40) {
            throw IllegalArgumentException("总金额不能超过30美金")
        }
        return if (multiSize == 1) {
            price()
        } else if (multiSize > 10) {
            BigDecimal.valueOf(0.51)
        } else {
            BigDecimal.valueOf(5).divide(BigDecimal(multiSize), 2, RoundingMode.HALF_UP)
        }
    }

    companion object {
        fun of(
            order: SubOrder,
            waybill: Waybill,
        ): List<WaybillRequest> =
            if (order.product.onSet()) {
                buildList {
                    for (setSkuSize in order.product.setSizeList()) {
                        add(
                            WaybillRequest(
                                orderNo = order.orderNo!!,
                                spu = order.product.spu!!,
                                size = setSkuSize,
                                color = order.product.color!!,
                                qty = order.product.qty,
                                weight = waybill.product.weight,
                                price = order.product.price,
                                currency = order.product.currency,
                                name = waybill.product.name,
                                cnName = order.product.cnName,
                                title = order.product.title,
                                supplierName = order.product.supplierName ?: "",
                                detail = order.product.detail!!,
                                country = order.recipient.country ?: "",
                                material = waybill.product.material,
                                hsCode = waybill.product.hsCode,
                                totalPrice = waybill.totalPrice,
                            ),
                        )
                    }
                }
            } else {
                listOf(
                    WaybillRequest(
                        orderNo = order.orderNo!!,
                        spu = order.product.spu!!,
                        size = order.product.size!!,
                        color = order.product.color!!,
                        qty = order.product.qty,
                        weight = waybill.product.weight,
                        price = order.product.price,
                        currency = order.product.currency,
                        name = waybill.product.name,
                        cnName = order.product.cnName,
                        title = order.product.title,
                        supplierName = order.product.supplierName ?: "",
                        detail = order.product.detail ?: "",
                        country = order.recipient.country ?: "",
                        material = waybill.product.material,
                        hsCode = waybill.product.hsCode,
                        totalPrice = waybill.totalPrice,
                    ),
                )
            }
    }
}
